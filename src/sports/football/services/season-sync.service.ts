import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CacheService } from '../../../core';
import { UtilsService, ImageService } from '../../../shared';
import { Fixture } from '../models/fixture.entity';
import { League } from '../models/league.entity';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class SeasonSyncService {
    private readonly logger = new Logger(SeasonSyncService.name);
    private readonly BATCH_SIZE = 100;
    private readonly MAX_CONCURRENT_REQUESTS = 1;

    constructor(
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        @InjectRepository(League)
        private readonly leagueRepository: Repository<League>,
        private readonly cacheService: CacheService,
        private readonly configService: ConfigService,
        private readonly utilsService: UtilsService,
        private readonly imageService: ImageService,
    ) { }

    async syncSeasonFixtures(): Promise<void> {
        this.logger.debug('Starting season fixtures sync');

        // Bước 1: Lấy danh sách league active và season
        const activeLeagues = await this.leagueRepository.find({
            where: { active: true },
            select: ['externalId', 'season'],
        });

        if (activeLeagues.length === 0) {
            this.logger.warn('No active leagues found');
            return;
        }

        // Bước 2: Gọi API tuần tự cho từng league/season
        const results = [];
        for (let i = 0; i < activeLeagues.length; i += this.MAX_CONCURRENT_REQUESTS) {
            const batchLeagues = activeLeagues.slice(i, i + this.MAX_CONCURRENT_REQUESTS);
            const batchResults = await Promise.all(
                batchLeagues.map(league => this.fetchSeasonFixtures(league.externalId, league.season))
            );
            results.push(...batchResults);
        }

        // Tổng hợp fixtures
        const allFixtures = results.flat().filter(fixture => fixture !== null);
        this.logger.debug(`Fetched ${allFixtures.length} fixtures from API`);

        // Bước 3: Upsert fixtures theo lô song song
        const batches = [];
        for (let i = 0; i < allFixtures.length; i += this.BATCH_SIZE) {
            batches.push(allFixtures.slice(i, i + this.BATCH_SIZE));
        }

        await Promise.all(
            batches.map(async (batch, index) => {
                // Process each fixture in the batch individually to handle constraint violations
                let upsertedCount = 0;
                for (const fixture of batch) {
                    try {
                        await this.fixtureRepository.upsert([fixture], ['externalId']);
                        upsertedCount++;
                    } catch (error) {
                        if (error.code === '23505') {
                            this.logger.debug(`Fixture with externalId ${fixture.externalId} already exists, skipping...`);
                        } else {
                            this.logger.error(`Failed to upsert fixture ${fixture.externalId}: ${error.message}`);
                            throw error;
                        }
                    }
                }
                this.logger.debug(`Upserted batch ${index + 1}/${batches.length}: ${upsertedCount}/${batch.length} fixtures`);
            })
        );

        await this.cacheService.deleteByPattern('fixtures_list_*');
        this.logger.debug('Cleared fixtures cache after season sync');
    }

    private async fetchSeasonFixtures(leagueId: number, season: number): Promise<Fixture[]> {
        try {
            const response = await axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures`, {
                params: {
                    league: leagueId,
                    season,
                    timezone: 'UTC',
                },
                headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
            });

            if (!response.data || !Array.isArray(response.data.response) || response.data.response.length === 0) {
                this.logger.debug(`No fixtures returned from API for league ${leagueId}, season ${season}`);
                return [];
            }

            const fixtures: Fixture[] = await Promise.all(
                response.data.response.map(async (apiData: any) => {
                    const fixture = new Fixture();
                    fixture.externalId = apiData.fixture.id;
                    fixture.leagueId = apiData.league.id;
                    fixture.leagueName = apiData.league.name || 'Unknown';
                    fixture.season = apiData.league.season || 0;
                    fixture.round = apiData.league.round || '';
                    fixture.homeTeamId = apiData.teams.home.id;
                    fixture.awayTeamId = apiData.teams.away.id;
                    fixture.slug = this.utilsService.generateSlug(
                        `${apiData.teams.home.name || 'home'}-vs-${apiData.teams.away.name || 'away'}`,
                        this.utilsService.formatDate(new Date(apiData.fixture.date)),
                    );
                    fixture.date = this.utilsService.parseUtcDate(apiData.fixture.date);
                    fixture.venueId = apiData.fixture.venue?.id || 0;
                    fixture.venueName = apiData.fixture.venue?.name || '';
                    fixture.venueCity = apiData.fixture.venue?.city || '';
                    fixture.referee = apiData.fixture.referee || '';
                    fixture.source = 'api';
                    fixture.createdBy = null;
                    fixture.timestamp = apiData.fixture.timestamp || Math.floor(Date.now() / 1000);
                    fixture.isHot = false;

                    // Download team logos
                    const homeTeamLogoPath = apiData.teams.home.logo
                        ? await this.downloadTeamLogo(apiData.teams.home.logo, apiData.teams.home.id)
                        : '';
                    const awayTeamLogoPath = apiData.teams.away.logo
                        ? await this.downloadTeamLogo(apiData.teams.away.logo, apiData.teams.away.id)
                        : '';

                    fixture.data = {
                        homeTeamName: apiData.teams.home.name || 'Unknown',
                        homeTeamLogo: homeTeamLogoPath || apiData.teams.home.logo || '',
                        awayTeamName: apiData.teams.away.name || 'Unknown',
                        awayTeamLogo: awayTeamLogoPath || apiData.teams.away.logo || '',
                        status: apiData.fixture.status?.short || 'NS',
                        statusLong: apiData.fixture.status?.long || 'Not Started',
                        statusExtra: apiData.fixture.status?.extra || 0,
                        elapsed: apiData.fixture.status?.elapsed || 0,
                        goalsHome: apiData.goals?.home ?? 0,
                        goalsAway: apiData.goals?.away ?? 0,
                        scoreHalftimeHome: apiData.score?.halftime?.home ?? 0,
                        scoreHalftimeAway: apiData.score?.halftime?.away ?? 0,
                        scoreFulltimeHome: apiData.score?.fulltime?.home ?? 0,
                        scoreFulltimeAway: apiData.score?.fulltime?.away ?? 0,
                        periods: {
                            first: apiData.fixture.periods?.first || 0,
                            second: apiData.fixture.periods?.second || 0,
                        },
                    };
                    return fixture;
                })
            );

            this.logger.debug(`Fetched ${fixtures.length} fixtures for league ${leagueId}, season ${season}`);
            return fixtures;
        } catch (error) {
            this.logger.error(`Failed to fetch fixtures for league ${leagueId}, season ${season}: ${error.message}, Status: ${error.response?.status || 'unknown'}`);
            return [];
        }
    }

    /**
     * Download team logo with error handling
     * @param logoUrl - URL of the team logo
     * @param teamId - Team ID for filename
     * @returns Local file path or empty string if failed
     */
    private async downloadTeamLogo(logoUrl: string, teamId: number): Promise<string> {
        try {
            return await this.imageService.downloadImage(logoUrl, 'teams', `${teamId}.png`);
        } catch (error) {
            this.logger.warn(`Failed to download team logo for team ${teamId}: ${error.message}`);
            return '';
        }
    }

    /**
     * Check if fixture status is live or upcoming (should not be overwritten)
     * @param status - Fixture status
     * @returns True if status should be protected from overwrite
     */
    private isLiveOrUpcomingStatus(status: string): boolean {
        const liveStatuses = [
            '1H',    // First Half
            'HT',    // Halftime
            '2H',    // Second Half
            'ET',    // Extra Time
            'P',     // Penalty
            'LIVE',  // Live (generic)
            'live',   // Live (lowercase)
            'Live'   // Live (lowercase)
        ];

        const upcomingStatuses = [
            'TBD',   // To Be Determined
            'NS'     // Not Started (but scheduled)
        ];

        return liveStatuses.includes(status) || upcomingStatuses.includes(status);
    }

    /**
     * Check if fixture is far enough in the future to be safe for upsert (UTC-safe)
     * @param fixtureDate - Fixture date
     * @param bufferMinutes - Buffer time in minutes (default: 5)
     * @returns True if fixture is safe to upsert, false if should be skipped
     */
    private isFixtureSafeForDailySync(fixtureDate: Date, bufferMinutes: number = 5): boolean {
        // Use UTC-safe time calculation
        const minutesDiff = this.utilsService.getMinutesDifference(fixtureDate);

        // Only safe if fixture is more than 5 minutes in the future
        // This leverages the fact that syncLiveFixtures runs every 10s
        // so any fixture within 5 minutes will have accurate live status
        return minutesDiff > bufferMinutes;
    }

    /**
     * Pure time-based smart upsert that leverages live sync system
     * @param fixtures - Fixtures to upsert
     * @returns Number of fixtures actually upserted
     */
    private async smartUpsertFixtures(fixtures: Fixture[]): Promise<number> {
        if (fixtures.length === 0) return 0;

        try {
            // Pure time-based filtering - no database queries needed
            // Trust that syncLiveFixtures (every 10s) maintains accurate status
            const safeFixtures: Fixture[] = [];
            const skippedFixtures: Fixture[] = [];

            for (const fixture of fixtures) {
                if (this.isFixtureSafeForDailySync(fixture.date)) {
                    // Fixture is >5 minutes in future, safe to upsert
                    // syncLiveFixtures will handle any status changes as match approaches
                    safeFixtures.push(fixture);
                } else {
                    // Fixture is within 5 minutes or in past
                    // Skip to avoid interfering with live sync system
                    skippedFixtures.push(fixture);
                }
            }

            this.logger.debug(`Pure time-based filtering: ${safeFixtures.length} safe fixtures (>5 min), ${skippedFixtures.length} skipped fixtures (≤5 min)`);

            let totalUpserted = 0;

            // Direct upsert for safe fixtures (no protection check needed)
            if (safeFixtures.length > 0) {
                await this.fixtureRepository.upsert(safeFixtures, ['externalId']);
                totalUpserted += safeFixtures.length;
                this.logger.debug(`Direct upserted ${safeFixtures.length} safe fixtures (>5 min in future)`);
            }

            // Log skipped fixtures for monitoring
            if (skippedFixtures.length > 0) {
                this.logger.log(`Skipped ${skippedFixtures.length} fixtures within 5 minutes (protected by live sync system)`);

                // Optional: Log some examples for debugging
                const exampleSkipped = skippedFixtures.slice(0, 3).map(f => ({
                    externalId: f.externalId,
                    date: f.date.toISOString(),
                    minutesFromNow: this.utilsService.getMinutesDifference(f.date)
                }));

                if (exampleSkipped.length > 0) {
                    this.logger.debug(`Example skipped fixtures: ${JSON.stringify(exampleSkipped)}`);
                }
            }

            // Summary statistics
            this.logger.log(`Time-based protection summary: ${totalUpserted} upserted, ${skippedFixtures.length} protected by live sync system`);

            return totalUpserted;

        } catch (error) {
            this.logger.error(`Smart upsert failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Trigger daily sync of all active league fixtures
     * This will manually execute the daily cronjob logic with FULL data sync
     * @returns Sync status and result
     */
    async triggerDailySync(): Promise<{ status: string; message: string; success: boolean; stats?: any }> {
        try {
            this.logger.log('Manual trigger: Starting FULL daily sync of all active league fixtures');

            // Get active leagues for sync
            const activeLeagues = await this.leagueRepository.find({
                where: { active: true },
                select: ['externalId', 'season'],
            });

            if (activeLeagues.length === 0) {
                return {
                    status: 'Warning',
                    message: 'No active leagues found for daily sync',
                    success: false
                };
            }

            const startTime = this.utilsService.getUtcNow();
            let totalFixturesProcessed = 0;
            let totalFixturesUpserted = 0;
            let processedLeagues = 0;
            const errors: string[] = [];

            // Process leagues in smaller batches for manual trigger
            const BATCH_SIZE = 3; // Smaller batch for manual trigger to avoid timeout
            const leagueBatches = [];
            for (let i = 0; i < activeLeagues.length; i += BATCH_SIZE) {
                leagueBatches.push(activeLeagues.slice(i, i + BATCH_SIZE));
            }

            this.logger.log(`Processing ${activeLeagues.length} leagues in ${leagueBatches.length} batches`);

            for (const [batchIndex, batch] of leagueBatches.entries()) {
                try {
                    this.logger.debug(`Processing batch ${batchIndex + 1}/${leagueBatches.length} with ${batch.length} leagues`);

                    // Fetch fixtures for each league in batch
                    const batchPromises = batch.map(async (league) => {
                        try {
                            return await this.fetchSeasonFixtures(league.externalId, league.season);
                        } catch (error) {
                            errors.push(`League ${league.externalId}: ${error.message}`);
                            return [];
                        }
                    });

                    const batchResults = await Promise.all(batchPromises);
                    const allFixtures = batchResults.flat().filter(fixture => fixture !== null);

                    totalFixturesProcessed += allFixtures.length;
                    processedLeagues += batch.length;

                    // Upsert fixtures to database in smaller chunks with parallel processing
                    if (allFixtures.length > 0) {
                        const UPSERT_BATCH_SIZE = 50; // Smaller chunks for manual trigger
                        const upsertBatches = [];
                        for (let i = 0; i < allFixtures.length; i += UPSERT_BATCH_SIZE) {
                            upsertBatches.push(allFixtures.slice(i, i + UPSERT_BATCH_SIZE));
                        }

                        // ✅ SMART PARALLEL upsert with live/upcoming protection
                        const upsertPromises = upsertBatches.map(async (upsertBatch, chunkIndex) => {
                            try {
                                const actualUpserted = await this.smartUpsertFixtures(upsertBatch);
                                this.logger.debug(`Batch ${batchIndex + 1}, Chunk ${chunkIndex + 1}: Smart upserted ${actualUpserted}/${upsertBatch.length} fixtures`);
                                return actualUpserted;
                            } catch (error) {
                                this.logger.error(`Batch ${batchIndex + 1}, Chunk ${chunkIndex + 1} smart upsert failed: ${error.message}`);
                                errors.push(`Batch ${batchIndex + 1}, Chunk ${chunkIndex + 1}: ${error.message}`);
                                return 0;
                            }
                        });

                        const upsertResults = await Promise.all(upsertPromises);
                        const batchUpsertCount = upsertResults.reduce((sum, count) => sum + count, 0);
                        totalFixturesUpserted += batchUpsertCount;

                        this.logger.debug(`Batch ${batchIndex + 1}: Smart upserted ${batchUpsertCount}/${allFixtures.length} fixtures in ${upsertBatches.length} parallel chunks (protected live/upcoming)`);
                    }

                    // Small delay between batches
                    if (batchIndex < leagueBatches.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 2000)); // 2s delay
                    }

                } catch (error) {
                    errors.push(`Batch ${batchIndex + 1} error: ${error.message}`);
                    this.logger.error(`Batch ${batchIndex + 1} failed: ${error.message}`);
                }
            }

            // Clear cache after sync
            await this.cacheService.deleteByPattern('fixtures_list_*');
            this.logger.debug('Cleared fixtures cache after manual daily sync');

            const endTime = this.utilsService.getUtcNow();
            const duration = endTime.getTime() - startTime.getTime();

            this.logger.log(`Manual daily sync completed: ${processedLeagues}/${activeLeagues.length} leagues, ${totalFixturesUpserted} fixtures upserted`);

            return {
                status: 'Success',
                message: `Daily sync completed successfully`,
                success: true,
                stats: {
                    totalLeagues: activeLeagues.length,
                    processedLeagues,
                    fixturesProcessed: totalFixturesProcessed,
                    fixturesUpserted: totalFixturesUpserted,
                    duration: `${Math.round(duration / 1000)}s`,
                    errors: errors.length > 0 ? errors.slice(0, 5) : [], // Show max 5 errors
                    note: 'This is a FULL sync with database upsert operations.'
                }
            };

        } catch (error) {
            this.logger.error(`Manual daily sync failed: ${error.message}`);
            return {
                status: 'Error',
                message: `Failed to trigger daily sync: ${error.message}`,
                success: false
            };
        }
    }

}