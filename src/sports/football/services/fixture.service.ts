import { Injectable, NotFoundException, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, DataSource } from 'typeorm';
import axios from 'axios';
import { Fixture } from '../models/fixture.entity';
import { League } from '../models/league.entity';
import { Team } from '../models/team.entity';
import { GetFixturesDto, FixtureResponseDto, PaginatedFixturesResponse, CreateFixtureDto, UpdateFixtureDto } from '../models/fixture.dto';
import { CacheService } from '../../../core';
import { UtilsService, ImageService } from '../../../shared';
import { GetScheduleDto } from '../models/schedule.dto';

@Injectable()
export class FixtureService {
    private readonly logger = new Logger(FixtureService.name);

    constructor(
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        @InjectRepository(League)
        private readonly leagueRepository: Repository<League>,
        @InjectRepository(Team)
        private readonly teamRepository: Repository<Team>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly utilsService: UtilsService,
        private readonly imageService: ImageService,
        private readonly dataSource: DataSource,
    ) { }


    async getTeamSchedule(teamId: number, query: GetScheduleDto): Promise<PaginatedFixturesResponse> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const cacheKey = `team_schedule_active_${teamId}_${query.season ?? ''}_${query.dateFrom ?? ''}_${query.dateTo ?? ''}_${page}_${limit}`;

        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning team schedule from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        const qb = this.fixtureRepository.createQueryBuilder('fixture')
            .innerJoin('leagues', 'league', 'league.externalId = fixture.leagueId AND league.season = fixture.season')
            .andWhere('league.active = :active', { active: true })
            .andWhere('fixture.homeTeamId = :teamId OR fixture.awayTeamId = :teamId', { teamId });
        if (query.season) {
            qb.andWhere('fixture.season = :season', { season: query.season });
        }
        if (query.dateFrom) {
            qb.andWhere('fixture.date >= :dateFrom', { dateFrom: query.dateFrom });
        }
        if (query.dateTo) {
            qb.andWhere('fixture.date <= :dateTo', { dateTo: query.dateTo });
        }
        qb.orderBy('fixture.date', 'ASC'); // Default order by date
        const [fixtures, totalItems] = await qb
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();

        const response: PaginatedFixturesResponse = {
            data: this.mapToResponseDto(fixtures),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        if (response.data.length > 0) {
            await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600);
            this.logger.debug(`Cached team schedule for key: ${cacheKey}`);
        }
        return response;
    }

    /**
   * Update an existing fixture
   * @param externalId - External ID of the fixture
   * @param updateFixtureDto - Fixture data to update
   * @returns Updated fixture
   */
    async updateFixture(externalId: number, updateFixtureDto: UpdateFixtureDto): Promise<FixtureResponseDto> {
        // Find existing fixture
        const fixture = await this.fixtureRepository.findOneBy({ externalId });
        if (!fixture) {
            throw new NotFoundException(`Fixture with externalId ${externalId} not found`);
        }

        // Validate league and teams if provided
        if (updateFixtureDto.leagueId && updateFixtureDto.season) {
            const league = await this.leagueRepository.findOne({
                where: { externalId: updateFixtureDto.leagueId, season: updateFixtureDto.season },
            });
            if (!league) {
                throw new BadRequestException(`League with externalId ${updateFixtureDto.leagueId} and season ${updateFixtureDto.season} not found`);
            }
            fixture.leagueId = updateFixtureDto.leagueId;
            fixture.leagueName = league.name;
            fixture.season = updateFixtureDto.season;
        }

        if (updateFixtureDto.homeTeamId) {
            const homeTeam = await this.teamRepository.findOneBy({ externalId: updateFixtureDto.homeTeamId });
            if (!homeTeam) {
                throw new BadRequestException(`Team not found: homeTeamId ${updateFixtureDto.homeTeamId}`);
            }
            fixture.homeTeamId = updateFixtureDto.homeTeamId;
        }

        if (updateFixtureDto.awayTeamId) {
            const awayTeam = await this.teamRepository.findOneBy({ externalId: updateFixtureDto.awayTeamId });
            if (!awayTeam) {
                throw new BadRequestException(`Team not found: awayTeamId ${updateFixtureDto.awayTeamId}`);
            }
            fixture.awayTeamId = updateFixtureDto.awayTeamId;
        }
        if (updateFixtureDto.isHot !== undefined) {
            fixture.isHot = updateFixtureDto.isHot;
        }
        // Update fields if provided
        if (updateFixtureDto.date) {
            fixture.date = new Date(new Date(updateFixtureDto.date).toISOString());
        }
        if (updateFixtureDto.round) {
            fixture.round = updateFixtureDto.round;
        }
        if (updateFixtureDto.venueId !== undefined) {
            fixture.venueId = updateFixtureDto.venueId;
        }
        if (updateFixtureDto.venueName !== undefined) {
            fixture.venueName = updateFixtureDto.venueName || '';
        }
        if (updateFixtureDto.venueCity !== undefined) {
            fixture.venueCity = updateFixtureDto.venueCity || '';
        }
        if (updateFixtureDto.referee !== undefined) {
            fixture.referee = updateFixtureDto.referee || '';
        }
        if (updateFixtureDto.timestamp) {
            fixture.timestamp = updateFixtureDto.timestamp;
        }
        if (updateFixtureDto.thumb !== undefined) {
            fixture.thumb = updateFixtureDto.thumb;
        }

        // Update data if provided
        if (updateFixtureDto.data) {
            fixture.data = {
                ...fixture.data,
                ...updateFixtureDto.data,
                homeTeamName: updateFixtureDto.data.homeTeamName || fixture.data.homeTeamName,
                homeTeamLogo: updateFixtureDto.data.homeTeamLogo || fixture.data.homeTeamLogo,
                awayTeamName: updateFixtureDto.data.awayTeamName || fixture.data.awayTeamName,
                awayTeamLogo: updateFixtureDto.data.awayTeamLogo || fixture.data.awayTeamLogo,
                status: updateFixtureDto.data.status || fixture.data.status,
                statusLong: updateFixtureDto.data.statusLong || fixture.data.statusLong,
                statusExtra: updateFixtureDto.data.statusExtra !== undefined ? updateFixtureDto.data.statusExtra : fixture.data.statusExtra,
                elapsed: updateFixtureDto.data.elapsed !== undefined ? updateFixtureDto.data.elapsed : fixture.data.elapsed,
                goalsHome: updateFixtureDto.data.goalsHome !== undefined ? updateFixtureDto.data.goalsHome : fixture.data.goalsHome,
                goalsAway: updateFixtureDto.data.goalsAway !== undefined ? updateFixtureDto.data.goalsAway : fixture.data.goalsAway,
                scoreHalftimeHome: updateFixtureDto.data.scoreHalftimeHome !== undefined ? updateFixtureDto.data.scoreHalftimeHome : fixture.data.scoreHalftimeHome,
                scoreHalftimeAway: updateFixtureDto.data.scoreHalftimeAway !== undefined ? updateFixtureDto.data.scoreHalftimeAway : fixture.data.scoreHalftimeAway,
                scoreFulltimeHome: updateFixtureDto.data.scoreFulltimeHome !== undefined ? updateFixtureDto.data.scoreFulltimeHome : fixture.data.scoreFulltimeHome,
                scoreFulltimeAway: updateFixtureDto.data.scoreFulltimeAway !== undefined ? updateFixtureDto.data.scoreFulltimeAway : fixture.data.scoreFulltimeAway,
                periods: updateFixtureDto.data.periods || fixture.data.periods,
            };
        }

        // Regenerate slug if necessary
        if (updateFixtureDto.data?.homeTeamName || updateFixtureDto.data?.awayTeamName || updateFixtureDto.date) {
            const homeTeamName = updateFixtureDto.data?.homeTeamName || fixture.data.homeTeamName;
            const awayTeamName = updateFixtureDto.data?.awayTeamName || fixture.data.awayTeamName;
            const date = updateFixtureDto.date || fixture.date.toISOString();
            const newSlug = this.utilsService.generateSlug(`${homeTeamName}-vs-${awayTeamName}`, date);

            if (newSlug !== fixture.slug) {
                const existingFixture = await this.fixtureRepository.findOneBy({ slug: newSlug });
                if (existingFixture && existingFixture.id !== fixture.id) {
                    throw new BadRequestException(`Fixture with slug ${newSlug} already exists`);
                }
                fixture.slug = newSlug;
            }
        }

        try {
            const savedFixture = await this.fixtureRepository.save(fixture);
            this.logger.debug(`Updated fixture with id ${savedFixture.id}`);

            // ✅ COMPREHENSIVE CACHE CLEARING (updated for active league filtering)
            await this.cacheService.deleteByPattern('fixtures_list_active_*');             // All fixture lists (active)
            await this.cacheService.deleteByPattern('fixtures_list_*');                     // Legacy cache keys
            await this.cacheService.deleteByPattern(`fixture_${fixture.externalId}_*`);     // Specific fixture
            await this.cacheService.deleteByPattern('team_schedule_active_*');              // Team schedules (active)
            await this.cacheService.deleteByPattern('team_schedule_*');                     // Legacy team schedules
            await this.cacheService.deleteByPattern('upcoming_live_fixtures_*');            // Live/upcoming fixtures
            this.logger.debug(`Cleared comprehensive fixture cache after update: External ID ${fixture.externalId}`);

            return this.mapToResponseDto([savedFixture])[0];
        } catch (error) {
            this.logger.error(`Failed to update fixture: ${error.message}`);
            throw new BadRequestException(`Failed to update fixture: ${error.message}`);
        }
    }
    /**
   * Create a new fixture manually
   * @param createFixtureDto - Fixture data
   * @returns Created fixture
   */
    async createFixture(createFixtureDto: CreateFixtureDto): Promise<FixtureResponseDto> {
        // Validate league and teams existence
        const league = await this.leagueRepository.findOne({
            where: { externalId: createFixtureDto.leagueId, season: createFixtureDto.season },
        });
        if (!league) {
            throw new BadRequestException(`League with externalId ${createFixtureDto.leagueId} and season ${createFixtureDto.season} not found`);
        }

        const homeTeam = await this.teamRepository.findOneBy({ externalId: createFixtureDto.homeTeamId });
        const awayTeam = await this.teamRepository.findOneBy({ externalId: createFixtureDto.awayTeamId });
        if (!homeTeam || !awayTeam) {
            throw new BadRequestException(`Team not found: homeTeamId ${createFixtureDto.homeTeamId} or awayTeamId ${createFixtureDto.awayTeamId}`);
        }

        // Generate slug
        const slug = this.utilsService.generateSlug(
            `${createFixtureDto.data.homeTeamName}-vs-${createFixtureDto.data.awayTeamName}`,
            this.utilsService.formatDate(new Date(createFixtureDto.date)),
        );
        console.log('Generated slug:', slug);

        // Check for duplicate slug
        const existingFixture = await this.fixtureRepository.findOneBy({ slug });
        if (existingFixture) {
            throw new BadRequestException(`Fixture with slug ${slug} already exists`);
        }

        // Generate unique externalId for manual fixtures if not provided
        let externalId = createFixtureDto.externalId;
        if (!externalId) {
            // Generate externalId in range 90000000-99999999 for manual fixtures
            // to avoid conflict with API fixtures (usually < 10000000)
            do {
                externalId = 90000000 + Math.floor(Math.random() * 10000000);
            } while (await this.fixtureRepository.findOneBy({ externalId }));
        }

        // Create fixture
        const fixture = new Fixture();
        fixture.isHot = createFixtureDto.isHot ?? false;
        fixture.externalId = externalId;
        fixture.leagueId = createFixtureDto.leagueId;
        fixture.leagueName = league.name;
        fixture.season = createFixtureDto.season;
        fixture.round = createFixtureDto.round || '';
        fixture.homeTeamId = createFixtureDto.homeTeamId;
        fixture.awayTeamId = createFixtureDto.awayTeamId;
        fixture.slug = slug;
        fixture.date = new Date(new Date(createFixtureDto.date).toISOString());
        fixture.venueId = createFixtureDto.venueId || 0;
        fixture.venueName = createFixtureDto.venueName || '';
        fixture.venueCity = createFixtureDto.venueCity || '';
        fixture.referee = createFixtureDto.referee || '';
        fixture.source = 'manual';
        fixture.createdBy = null; // Có thể thêm logic user authentication sau
        fixture.timestamp = createFixtureDto.timestamp || Math.floor(Date.now() / 1000);
        fixture.thumb = createFixtureDto.thumb;
        fixture.data = {
            homeTeamName: createFixtureDto.data.homeTeamName,
            homeTeamLogo: createFixtureDto.data.homeTeamLogo || homeTeam.logo || '',
            awayTeamName: createFixtureDto.data.awayTeamName,
            awayTeamLogo: createFixtureDto.data.awayTeamLogo || awayTeam.logo || '',
            status: createFixtureDto.data.status,
            statusLong: createFixtureDto.data.statusLong,
            statusExtra: createFixtureDto.data.statusExtra,
            elapsed: createFixtureDto.data.elapsed || 0,
            goalsHome: createFixtureDto.data.goalsHome || 0,
            goalsAway: createFixtureDto.data.goalsAway || 0,
            scoreHalftimeHome: createFixtureDto.data.scoreHalftimeHome || 0,
            scoreHalftimeAway: createFixtureDto.data.scoreHalftimeAway || 0,
            scoreFulltimeHome: createFixtureDto.data.scoreFulltimeHome || 0,
            scoreFulltimeAway: createFixtureDto.data.scoreFulltimeAway || 0,
            periods: createFixtureDto.data.periods || { first: 0, second: 0 },
        };

        try {
            const savedFixture = await this.fixtureRepository.save(fixture);
            this.logger.debug(`Saved manual fixture with id ${savedFixture.id}`);

            // Clear related cache (updated for active league filtering)
            await this.cacheService.deleteByPattern(`fixtures_list_active_${createFixtureDto.leagueId}_*`);
            await this.cacheService.deleteByPattern(`fixtures_list_${createFixtureDto.leagueId}_*`); // Legacy

            return this.mapToResponseDto([savedFixture])[0];
        } catch (error) {
            this.logger.error(`Failed to create fixture: ${error.message}`);
            throw new BadRequestException(`Failed to create fixture: ${error.message}`);
        }
    }


    /**
     * Get fixtures by query parameters
     * @param query - Query parameters (league, season, date, team, status, page, limit, newdb)
     * @returns List of fixtures
     */
    async getFixtures(query: GetFixturesDto): Promise<PaginatedFixturesResponse> {
        this.logger.debug(`🔍 [STEP 1] getFixtures called with query: ${JSON.stringify(query)}`);
        this.logger.debug(`🔍 [STEP 1a] newdb value: ${query.newdb}, type: ${typeof query.newdb}`);

        const page = query.page || 1;
        const limit = query.limit || 10;
        const cacheKey = `fixtures_list_active_${query.league ?? ''}_${query.season ?? ''}_${query.date ?? ''}_${query.team ?? ''}_${query.status ?? ''}_${query.isHot ?? ''}_${query.search ?? ''}_${page}_${limit}`;

        this.logger.debug(`🔍 [STEP 2] Generated cache key: ${cacheKey}`);

        if (query.newdb === true) {
            this.logger.debug(`🔍 [STEP 2a] ENTERING newdb=true branch`);
        } else {
            this.logger.debug(`🔍 [STEP 2a] SKIPPING newdb=true branch (newdb=${query.newdb})`);
        }

        if (query.newdb === true) {
            this.logger.debug(`newdb=true, fetching directly from API for query: ${JSON.stringify(query)}`);
            const fixtures = await this.fetchFromApi(query);
            let paginatedResult = { fixtures, totalItems: fixtures.length };

            if (fixtures.length > 0) {
                try {
                    await this.fixtureRepository.upsert(fixtures, ['externalId']);
                    this.logger.debug(`Upserted ${fixtures.length} fixtures to DB`);
                    paginatedResult = await this.fetchFromDb(query);
                } catch (error) {
                    this.logger.error(`Failed to save fixtures to DB: ${error.message}`);
                    throw error;
                }
            }

            await this.cacheService.deleteByPattern(`fixtures_list_active_${query.league ?? ''}_${query.season ?? ''}_${query.date ?? ''}_${query.team ?? ''}_*`);
            this.logger.debug('Cleared specific fixture cache after API fetch');

            const response: PaginatedFixturesResponse = {
                data: this.mapToResponseDto(paginatedResult.fixtures),
                meta: {
                    totalItems: paginatedResult.totalItems,
                    totalPages: Math.ceil(paginatedResult.totalItems / limit),
                    currentPage: page,
                    limit,
                },
                status: 200,
            };

            if (response.data.length > 0) {
                await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600);
                this.logger.debug(`Cached paginated response for key: ${cacheKey}`);
            }
            return response;
        }

        this.logger.debug(`🔍 [STEP 3] Checking cache for key: ${cacheKey}`);
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`✅ [STEP 3] Cache HIT - returning cached response`);
            const cachedResult: PaginatedFixturesResponse = JSON.parse(cached);
            return cachedResult;
        }
        this.logger.debug(`❌ [STEP 3] Cache MISS - proceeding to database`);

        this.logger.debug(`🔍 [STEP 4] Fetching from database...`);
        let { fixtures, totalItems } = await this.fetchFromDb(query);
        this.logger.debug(`🔍 [STEP 4] Database returned ${fixtures.length} fixtures`);

        if (fixtures.length === 0 && !query.id) {
            this.logger.debug(`🔍 [STEP 5] No fixtures found in DB, fetching from API...`);
            fixtures = await this.fetchFromApi(query);
            this.logger.debug(`🔍 [STEP 5] API returned ${fixtures.length} fixtures`);
            if (fixtures.length > 0) {
                this.logger.debug(`🔍 [STEP 6] Attempting to save ${fixtures.length} fixtures to DB...`);
                try {
                    // ✅ FIX: Manual duplicate checking before save
                    this.logger.debug(`🔍 [STEP 6a] Checking for existing fixtures...`);
                    const externalIds = fixtures.map(f => f.externalId);
                    this.logger.debug(`🔍 [STEP 6a] External IDs to check: ${externalIds.slice(0, 5).join(', ')}${externalIds.length > 5 ? '...' : ''}`);

                    const existingFixtures = await this.fixtureRepository.findBy({
                        externalId: In(externalIds)
                    });
                    this.logger.debug(`🔍 [STEP 6a] Found ${existingFixtures.length} existing fixtures`);

                    const existingIds = new Set(existingFixtures.map(f => f.externalId));
                    this.logger.debug(`🔍 [STEP 6a] Existing IDs: ${Array.from(existingIds).slice(0, 5).join(', ')}${existingIds.size > 5 ? '...' : ''}`);

                    // Only save fixtures that don't exist
                    const newFixtures = fixtures.filter(f => !existingIds.has(f.externalId));
                    this.logger.debug(`🔍 [STEP 6b] Filtered to ${newFixtures.length} new fixtures (${fixtures.length - newFixtures.length} already existed)`);

                    if (newFixtures.length > 0) {
                        this.logger.debug(`🔍 [STEP 6c] Saving ${newFixtures.length} new fixtures...`);
                        await this.fixtureRepository.save(newFixtures);
                        this.logger.debug(`✅ [STEP 6c] Successfully saved ${newFixtures.length} new fixtures`);
                    } else {
                        this.logger.debug(`✅ [STEP 6] All ${fixtures.length} fixtures already exist in DB - no save needed`);
                    }

                    this.logger.debug(`🔍 [STEP 6d] Re-fetching from DB after save...`);
                    const paginatedResult = await this.fetchFromDb(query);
                    fixtures = paginatedResult.fixtures;
                    totalItems = paginatedResult.totalItems;
                    this.logger.debug(`✅ [STEP 6d] Re-fetch completed, got ${fixtures.length} fixtures`);
                } catch (error) {
                    this.logger.error(`❌ [STEP 6] SAVE FAILED: ${error.message}`);
                    this.logger.error(`❌ [STEP 6] Error details:`, error);
                    throw error;
                }
            }
        }

        const response: PaginatedFixturesResponse = {
            data: this.mapToResponseDto(fixtures),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        if (response.data.length > 0) {
            await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600);
            this.logger.debug(`Cached paginated response for key: ${cacheKey}`);
        }

        return response;
    }


    /**
     * Get a single fixture by external ID
     * @param externalId - External ID of the fixture
     * @returns Fixture details
     */
    async getFixtureById(externalId: number): Promise<FixtureResponseDto> {
        let fixture = await this.fixtureRepository.findOneBy({ externalId });
        if (!fixture) {
            this.logger.log(`Fixture ${externalId} not found in DB, fetching from API`);
            const fixtures = await this.fetchFromApi({ id: externalId, page: 1, limit: 1 });
            if (fixtures.length === 0) {
                this.logger.warn(`Fixture ${externalId} not found in API`);
                throw new NotFoundException(`Fixture with externalId ${externalId} not found`);
            }
            fixture = fixtures[0];
            // ✅ FIX: Use upsert instead of save to handle duplicates
            await this.fixtureRepository.upsert([fixture], ['externalId']);
        }
        return this.mapToResponseDto([fixture])[0];
    }

    /**
     * Delete a fixture by external ID
     * @param externalId - External ID of the fixture
     */
    async deleteFixture(externalId: number): Promise<void> {
        // Find existing fixture
        const fixture = await this.fixtureRepository.findOneBy({ externalId });
        if (!fixture) {
            throw new NotFoundException(`Fixture with externalId ${externalId} not found`);
        }

        try {
            // Delete the fixture
            await this.fixtureRepository.delete({ externalId });
            this.logger.debug(`Deleted fixture with externalId ${externalId}`);

            // Clear related cache (updated for active league filtering)
            await this.cacheService.deleteByPattern(`fixtures_list_active_${fixture.leagueId}_*`);
            await this.cacheService.deleteByPattern(`fixtures_list_${fixture.leagueId}_*`); // Legacy
            await this.cacheService.deleteByPattern(`fixture_${externalId}_*`);
            this.logger.debug(`Cleared cache for deleted fixture ${externalId}`);
        } catch (error) {
            this.logger.error(`Failed to delete fixture: ${error.message}`);
            throw new BadRequestException(`Failed to delete fixture: ${error.message}`);
        }
    }

    /**
     * Fetch fixtures from database
     * @param query - Query parameters
     * @returns List of fixtures
     */

    private async fetchFromDb(query: GetFixturesDto): Promise<{ fixtures: Fixture[]; totalItems: number }> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;

        const qb = this.fixtureRepository.createQueryBuilder('fixture');

        // ✅ ENHANCED: Join with leagues table to filter only active leagues
        qb.innerJoin('leagues', 'league', 'league.externalId = fixture.leagueId AND league.season = fixture.season')
            .andWhere('league.active = :active', { active: true });

        if (query.id) qb.andWhere('fixture.externalId = :id', { id: query.id });
        if (query.league) qb.andWhere('fixture.leagueId = :league', { league: query.league });
        if (query.season) qb.andWhere('fixture.season = :season', { season: query.season });
        if (query.date) qb.andWhere('DATE(fixture.date) = :date', { date: query.date });
        if (query.team) qb.andWhere('fixture.homeTeamId = :team OR fixture.awayTeamId = :team', { team: query.team });
        if (query.status) qb.andWhere('fixture.data->>\'status\' = :status', { status: query.status });
        if (query.isHot !== undefined) {
            qb.andWhere('fixture.isHot = :isHot', { isHot: query.isHot });
        }
        if (query.search) {
            // Search in team names (both home and away teams)
            const searchTerm = `%${query.search.toLowerCase()}%`;
            qb.andWhere(
                '(LOWER(fixture.data->>\'homeTeamName\') LIKE :searchTerm OR LOWER(fixture.data->>\'awayTeamName\') LIKE :searchTerm)',
                { searchTerm }
            );
        }
        qb.orderBy('fixture.date', 'ASC'); // Default order by date

        const [fixtures, totalItems] = await qb
            .skip(skip)
            .take(limit)
            .getManyAndCount();

        this.logger.debug(`Fetched ${fixtures.length} fixtures from DB (active leagues only) for query: ${JSON.stringify(query)}`);
        return { fixtures, totalItems };
    }

    /**
     * Fetch fixtures from external API and process them
     * @param query - Query parameters
     * @returns List of processed fixtures
     */
    private async fetchFromApi(query: GetFixturesDto): Promise<Fixture[]> {
        try {
            // Loại bỏ page và limit khỏi query vì API Football không hỗ trợ
            const { page, limit, newdb, isHot, status, ...apiQuery } = query;
            const response = await this.executeWithRetry(async () => {
                return axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures`, {
                    params: { ...apiQuery, timezone: 'UTC' },
                    headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                });
            });

            this.logger.debug(`API response for query ${JSON.stringify(apiQuery)}: ${JSON.stringify(response.data)}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No data returned from API for query ${JSON.stringify(apiQuery)}`);
                return [];
            }

            // Bước 3: Thu thập leagues và teams song song
            const leagueMap = new Map<string, Partial<League>>();
            const teamMap = new Map<number, Partial<Team>>();

            await Promise.all(
                response.data.response.map(async (apiData: any) => {
                    try {
                        if (!apiData.league?.id || !apiData.league?.season) {
                            this.logger.warn(`Invalid league data for fixture ${apiData.fixture?.id || 'unknown'}`);
                            return;
                        }
                        const leagueKey = `${apiData.league.id}_${apiData.league.season}`;
                        if (!leagueMap.has(leagueKey)) {
                            const leagueLogoPath = apiData.league.logo
                                ? await this.imageService.downloadImage(apiData.league.logo, 'leagues', `${apiData.league.id}.png`)
                                : '';
                            const flagPath = apiData.league.flag
                                ? await this.imageService.downloadImage(apiData.league.flag, 'flags', `${apiData.league.id}.svg`)
                                : '';
                            leagueMap.set(leagueKey, {
                                externalId: apiData.league.id,
                                name: apiData.league.name || 'Unknown',
                                country: apiData.league.country || 'Unknown',
                                logo: leagueLogoPath,
                                flag: flagPath,
                                season: apiData.league.season || 0,
                                type: apiData.league.type?.toLowerCase() || 'unknown',
                            });
                        }

                        if (!apiData.teams?.home?.id || !apiData.teams?.away?.id) {
                            this.logger.warn(`Invalid teams data for fixture ${apiData.fixture?.id || 'unknown'}`);
                            return;
                        }
                        if (!teamMap.has(apiData.teams.home.id)) {
                            const homeTeamLogoPath = apiData.teams.home.logo
                                ? await this.imageService.downloadImage(apiData.teams.home.logo, 'teams', `${apiData.teams.home.id}.png`)
                                : '';
                            teamMap.set(apiData.teams.home.id, {
                                externalId: apiData.teams.home.id,
                                name: apiData.teams.home.name || 'Unknown',
                                logo: homeTeamLogoPath,
                            });
                        }
                        if (!teamMap.has(apiData.teams.away.id)) {
                            const awayTeamLogoPath = apiData.teams.away.logo
                                ? await this.imageService.downloadImage(apiData.teams.away.logo, 'teams', `${apiData.teams.away.id}.png`)
                                : '';
                            teamMap.set(apiData.teams.away.id, {
                                externalId: apiData.teams.away.id,
                                name: apiData.teams.away.name || 'Unknown',
                                logo: awayTeamLogoPath,
                            });
                        }
                    } catch (error) {
                        this.logger.error(`Error processing fixture ${apiData.fixture?.id || 'unknown'}: ${error.message}`);
                    }
                }),
            );

            // Bước 4: Kiểm tra leagues active trong database
            const activeLeagues = await this.getActiveLeagues([...leagueMap.keys()]);
            this.logger.debug(`Found ${activeLeagues.size} active leagues in database`);

            // Bước 5: Lưu leagues và teams song song trong transaction (chỉ cho active leagues)
            const activeLeagueData = [...leagueMap.entries()]
                .filter(([key]) => activeLeagues.has(key))
                .map(([, league]) => league);

            await this.dataSource.transaction(async (manager) => {
                await Promise.all([
                    this.processLeagues(activeLeagueData, manager.getRepository(League)),
                    this.processTeams([...teamMap.values()], manager.getRepository(Team)),
                ]);
            });

            // Bước 6: Xử lý fixtures song song (chỉ cho active leagues)
            const fixtures = await Promise.all(
                response.data.response.map(async (apiData: any) => {
                    try {
                        if (!apiData.fixture?.id || !apiData.league?.id || !apiData.teams?.home?.id || !apiData.teams?.away?.id) {
                            this.logger.warn(`Invalid fixture data for fixture ${apiData.fixture?.id || 'unknown'}`);
                            return null;
                        }

                        // Kiểm tra xem league có active không
                        const leagueKey = `${apiData.league.id}_${apiData.league.season}`;
                        if (!activeLeagues.has(leagueKey)) {
                            this.logger.debug(`Skipping fixture ${apiData.fixture.id} - league ${apiData.league.id} season ${apiData.league.season} is not active`);
                            return null;
                        }

                        const fixture = new Fixture();
                        fixture.externalId = apiData.fixture.id;
                        fixture.leagueId = apiData.league.id;
                        fixture.leagueName = apiData.league.name || 'Unknown';
                        fixture.season = apiData.league.season || 0;
                        fixture.round = apiData.league.round || '';
                        fixture.homeTeamId = apiData.teams.home.id;
                        fixture.awayTeamId = apiData.teams.away.id;
                        fixture.slug = this.utilsService.generateSlug(
                            `${apiData.teams.home.name || 'home'}-vs-${apiData.teams.away.name || 'away'}`,
                            this.utilsService.formatDate(new Date(apiData.fixture.date || new Date().toISOString())),
                        );
                        fixture.date = new Date(new Date(apiData.fixture.date).toISOString());
                        fixture.venueId = apiData.fixture.venue?.id || 0;
                        fixture.venueName = apiData.fixture.venue?.name || '';
                        fixture.venueCity = apiData.fixture.venue?.city || '';
                        fixture.referee = apiData.fixture.referee || '';
                        fixture.source = 'api';
                        fixture.createdBy = null;
                        fixture.timestamp = apiData.fixture.timestamp || Math.floor(Date.now() / 1000);
                        fixture.isHot = false;

                        fixture.data = {
                            homeTeamName: apiData.teams.home.name || 'Unknown',
                            homeTeamLogo: teamMap.get(apiData.teams.home.id)?.logo || '',
                            awayTeamName: apiData.teams.away.name || 'Unknown',
                            awayTeamLogo: teamMap.get(apiData.teams.away.id)?.logo || '',
                            status: apiData.fixture.status?.short || 'NS',
                            statusLong: apiData.fixture.status?.long || 'Not Started',
                            statusExtra: apiData.fixture.status?.extra || 0,
                            elapsed: apiData.fixture.status?.elapsed || 0,
                            goalsHome: apiData.goals?.home ?? 0,
                            goalsAway: apiData.goals?.away ?? 0,
                            scoreHalftimeHome: apiData.score?.halftime?.home ?? 0,
                            scoreHalftimeAway: apiData.score?.halftime?.away ?? 0,
                            scoreFulltimeHome: apiData.score?.fulltime?.home ?? 0,
                            scoreFulltimeAway: apiData.score?.fulltime?.away ?? 0,
                            periods: {
                                first: apiData.fixture.periods?.first || 0,
                                second: apiData.fixture.periods?.second || 0,
                            },
                        };
                        return fixture;
                    } catch (error) {
                        this.logger.error(`Error mapping fixture ${apiData.fixture?.id || 'unknown'}: ${error.message}`);
                        return null;
                    }
                }),
            );

            const validFixtures = fixtures.filter((fixture): fixture is Fixture => fixture !== null);
            this.logger.debug(`Processed ${validFixtures.length} valid fixtures from API`);
            return validFixtures;
        } catch (error) {
            this.logger.error(`Failed to fetch from API: ${error.message}`);
            return [];
        }
    }
    /**
     * Get active leagues from database
     * @param leagueKeys - Array of league keys in format "leagueId_season"
     * @returns Set of active league keys
     */
    private async getActiveLeagues(leagueKeys: string[]): Promise<Set<string>> {
        if (leagueKeys.length === 0) return new Set();

        // Parse league keys to get league IDs and seasons
        const leagueConditions = leagueKeys.map(key => {
            const [leagueId, season] = key.split('_');
            return { externalId: parseInt(leagueId), season: parseInt(season) };
        });

        // Query active leagues
        const activeLeagues = await this.leagueRepository
            .createQueryBuilder('league')
            .where('league.active = :active', { active: true })
            .andWhere(
                leagueConditions.map((_, index) =>
                    `(league.externalId = :leagueId${index} AND league.season = :season${index})`
                ).join(' OR '),
                leagueConditions.reduce((params, condition, index) => {
                    params[`leagueId${index}`] = condition.externalId;
                    params[`season${index}`] = condition.season;
                    return params;
                }, {} as any)
            )
            .getMany();

        // Convert to Set of keys
        const activeLeagueKeys = new Set(
            activeLeagues.map(league => `${league.externalId}_${league.season}`)
        );

        this.logger.debug(`Active leagues found: ${Array.from(activeLeagueKeys).join(', ')}`);
        return activeLeagueKeys;
    }

    /**
     * Process and upsert leagues
     * @param leagues - List of leagues to process
     * @param repository - League repository
     */
    private async processLeagues(leagues: Partial<League>[], repository: Repository<League>): Promise<void> {
        if (leagues.length === 0) return;

        const externalIds = leagues.map((league) => league.externalId);
        const seasons = leagues.map((league) => league.season);
        const existingLeagues = await repository.findBy({
            externalId: In(externalIds),
            season: In(seasons),
        });

        const existingMap = new Map<string, League>(
            existingLeagues.map((league) => [`${league.externalId}_${league.season}`, league]),
        );

        const leaguesToUpsert = leagues.filter((league) => {
            const key = `${league.externalId}_${league.season}`;
            return !existingMap.has(key);
        });

        if (leaguesToUpsert.length > 0) {
            try {
                await repository.upsert(leaguesToUpsert, ['externalId', 'season']);
                this.logger.debug(`Upserted ${leaguesToUpsert.length} leagues`);
            } catch (error) {
                this.logger.error(`Failed to upsert leagues: ${error.message}`);
                throw error;
            }
        }
    }

    /**
     * Process and upsert teams
     * @param teams - List of teams to process
     * @param repository - Team repository
     */
    private async processTeams(teams: Partial<Team>[], repository: Repository<Team>): Promise<void> {
        if (teams.length === 0) return;

        const externalIds = teams.map((team) => team.externalId);
        const existingTeams = await repository.findBy({ externalId: In(externalIds) });
        const existingMap = new Map<number, Team>(
            existingTeams.map((team) => [team.externalId, team]),
        );

        const teamsToUpsert = teams.filter((team) => !existingMap.has(team.externalId!));

        if (teamsToUpsert.length > 0) {
            try {
                await repository.upsert(teamsToUpsert, ['externalId']);
                this.logger.debug(`Upserted ${teamsToUpsert.length} teams`);
            } catch (error) {
                this.logger.error(`Failed to upsert teams: ${error.message}`);
                throw error;
            }
        }
    }

    /**
     * Execute a function with retry logic
     * @param fn - Function to execute
     * @param retries - Number of retries
     * @param delay - Delay between retries (ms)
     * @returns Result of the function
     */
    private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                if (attempt === retries) {
                    this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                    throw error;
                }
                this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                await new Promise((resolve) => setTimeout(resolve, delay));
            }
        }
        throw new Error('Unexpected error in retry logic');
    }

    /**
     * Map fixtures to response DTO
     * @param fixtures - List of fixtures
     * @returns List of response DTOs
     */
    public mapToResponseDto(fixtures: Fixture[]): FixtureResponseDto[] {
        return fixtures.map((fixture) => ({
            id: fixture.id,
            externalId: fixture.externalId,
            leagueId: fixture.leagueId,
            leagueName: fixture.leagueName,
            isHot: fixture.isHot,
            season: fixture.season,
            round: fixture.round,
            homeTeamId: fixture.homeTeamId,
            homeTeamName: fixture.data.homeTeamName,
            homeTeamLogo: fixture.data.homeTeamLogo,
            awayTeamId: fixture.awayTeamId,
            awayTeamName: fixture.data.awayTeamName,
            awayTeamLogo: fixture.data.awayTeamLogo,
            slug: fixture.slug,
            date: fixture.date instanceof Date ? fixture.date.toISOString() : new Date(fixture.date).toISOString(),
            venue: {
                id: fixture.venueId,
                name: fixture.venueName,
                city: fixture.venueCity,
            },
            referee: fixture.referee,
            status: fixture.data.status,
            statusLong: fixture.data.statusLong,
            statusExtra: fixture.data.statusExtra,
            elapsed: fixture.data.elapsed,
            goalsHome: fixture.data.goalsHome,
            goalsAway: fixture.data.goalsAway,
            scoreHalftimeHome: fixture.data.scoreHalftimeHome,
            scoreHalftimeAway: fixture.data.scoreHalftimeAway,
            scoreFulltimeHome: fixture.data.scoreFulltimeHome,
            scoreFulltimeAway: fixture.data.scoreFulltimeAway,
            periods: fixture.data.periods,
            timestamp: fixture.timestamp,
            thumb: fixture.thumb,
        }));
    }
}